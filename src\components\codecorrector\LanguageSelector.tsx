import React from 'react';
import { ChevronDown } from 'lucide-react';

interface LanguageSelectorProps {
  selectedLanguage: string;
  onSelectLanguage: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onSelectLanguage
}) => {
  const languages = [
    { id: 'javascript', name: 'JavaScript' },
    { id: 'typescript', name: 'TypeScript' },
    { id: 'python', name: 'Python' },
    { id: 'java', name: 'Java' },
    { id: 'csharp', name: 'C#' },
    { id: 'cpp', name: 'C++' },
    { id: 'php', name: 'PHP' },
    { id: 'ruby', name: '<PERSON>' },
    { id: 'go', name: 'Go' },
    { id: 'rust', name: 'Rust' },
    { id: 'swift', name: '<PERSON>' },
    { id: 'kotlin', name: '<PERSON><PERSON><PERSON>' },
    { id: 'html', name: '<PERSON><PERSON><PERSON>' },
    { id: 'css', name: 'CS<PERSON>' },
    { id: 'sql', name: 'SQL' }
  ];

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-codestorm-gold mb-1">Lenguaje</label>
      <div className="relative">
        <select
          value={selectedLanguage}
          onChange={(e) => onSelectLanguage(e.target.value)}
          className="appearance-none w-full bg-codestorm-darker border border-codestorm-blue/30 rounded-md py-2 pl-3 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-codestorm-accent/50 focus:border-codestorm-accent/50"
        >
          {languages.map((language) => (
            <option key={language.id} value={language.id}>
              {language.name}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
          <ChevronDown className="h-4 w-4" />
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
