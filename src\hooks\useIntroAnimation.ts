import { useState, useEffect } from 'react';

/**
 * Hook personalizado para manejar la animación de introducción
 * @param pageKey - Clave única para identificar la página (ej: 'home', 'menu')
 * @returns objeto con showIntro, completeIntro y resetIntro
 */
const useIntroAnimation = (pageKey?: string) => {
  const [showIntro, setShowIntro] = useState<boolean>(false);
  
  // Generar clave única para localStorage basada en la página
  const storageKey = `codestorm-intro-seen-${pageKey || 'default'}`;

  useEffect(() => {
    try {
      // ALWAYS show intro animation - fresh experience every time!
      // This ensures users always see the beautiful intro animation
      console.log(`🎬 ALWAYS showing intro animation for ${pageKey || 'página principal'} - Fresh experience guaranteed!`);
      setShowIntro(true);
    } catch (error) {
      console.error('Error al acceder a localStorage:', error);
      // Even in case of error, show the animation for better UX
      setShowIntro(true);
    }
  }, [storageKey, pageKey]);

  /**
   * Función para completar y ocultar la animación (sin guardar en localStorage)
   */
  const completeIntro = () => {
    // DO NOT save to localStorage - we want the animation to always show
    console.log(`🎬 Intro animation completed for ${pageKey || 'página principal'} - Will show again on next visit!`);
    
    // Ocultar la animación
    setShowIntro(false);
  };

  /**
   * Función para resetear el estado de la animación (para pruebas)
   */
  const resetIntro = () => {
    try {
      // Eliminar la marca de localStorage
      localStorage.removeItem(storageKey);
      console.log(`Estado de la animación reseteado para ${pageKey || 'página principal'}`);
    } catch (error) {
      console.error('Error al eliminar de localStorage:', error);
    }

    // Mostrar la animación inmediatamente
    setShowIntro(true);
  };

  return {
    showIntro,
    completeIntro,
    resetIntro
  };
};

export default useIntroAnimation;
