/* Animaciones para CODESTORM */

/* Animación de fade in para nuevos mensajes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Animación de pulso sutil */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Efecto de brillo (glow) */
.shadow-glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

/* Animaciones del spinner */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes float-small {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) scale(1.2);
    opacity: 1;
  }
}

/* Clases de utilidad para animaciones */
.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-small {
  animation: float-small 3s ease-in-out infinite;
}

/* Delays para animaciones escalonadas */
.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-450 {
  animation-delay: 450ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

/* Animación de aparición para el overlay */
@keyframes overlay-appear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-overlay-appear {
  animation: overlay-appear 0.3s ease-out;
}

/* Animación de partículas */
@keyframes particle-float {
  0% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-10px) translateX(5px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) translateX(-5px) scale(1.2);
    opacity: 1;
  }
  75% {
    transform: translateY(-10px) translateX(3px) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
}

.animate-particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

/* Animación de ondas para el loading */
@keyframes wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-15px);
  }
}

.animate-wave {
  animation: wave 1.5s ease-in-out infinite;
}

/* Animación de typing indicator */
@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.animate-typing {
  animation: typing 1.4s ease-in-out infinite;
}

/* Animación de brillo en barras de progreso */
@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-progress-shine {
  animation: progress-shine 2s ease-in-out infinite;
}

/* Hover effects para botones */
.btn-hover-glow:hover {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  transform: translateY(-1px);
  transition: all 0.2s ease-out;
}

.btn-hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-out;
}

/* Animación de carga de archivos */
@keyframes file-load {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-file-load {
  animation: file-load 0.4s ease-out;
}

/* Animación de éxito */
@keyframes success-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-success-bounce {
  animation: success-bounce 1s ease-out;
}

/* Animación de error shake */
@keyframes error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

.animate-error-shake {
  animation: error-shake 0.6s ease-out;
}

/* Transiciones suaves para cambios de estado */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== ANIMACIONES DE BORDE PULSANTE PARA CHAT ===== */

/* Animación de pulso de borde con brillo azul */
@keyframes chat-border-pulse {
  0%, 100% {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 0 1px rgba(59, 130, 246, 0.1),
      0 0 10px rgba(59, 130, 246, 0.1),
      inset 0 0 10px rgba(59, 130, 246, 0.05);
  }
  50% {
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow:
      0 0 0 1px rgba(59, 130, 246, 0.3),
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 30px rgba(59, 130, 246, 0.1),
      inset 0 0 15px rgba(59, 130, 246, 0.1);
  }
}

/* Animación de pulso más sutil para mensajes del usuario */
@keyframes chat-border-pulse-user {
  0%, 100% {
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow:
      0 0 0 1px rgba(139, 92, 246, 0.1),
      0 0 8px rgba(139, 92, 246, 0.1),
      inset 0 0 8px rgba(139, 92, 246, 0.05);
  }
  50% {
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow:
      0 0 0 1px rgba(139, 92, 246, 0.2),
      0 0 15px rgba(139, 92, 246, 0.2),
      0 0 25px rgba(139, 92, 246, 0.1),
      inset 0 0 12px rgba(139, 92, 246, 0.08);
  }
}

/* Animación de pulso para mensajes del sistema */
@keyframes chat-border-pulse-system {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.3);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.1),
      0 0 8px rgba(34, 197, 94, 0.1),
      inset 0 0 8px rgba(34, 197, 94, 0.05);
  }
  50% {
    border-color: rgba(34, 197, 94, 0.5);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.2),
      0 0 15px rgba(34, 197, 94, 0.2),
      0 0 25px rgba(34, 197, 94, 0.1),
      inset 0 0 12px rgba(34, 197, 94, 0.08);
  }
}

/* Animación de respiración suave para contenedores de código */
@keyframes chat-border-pulse-code {
  0%, 100% {
    border-color: rgba(251, 191, 36, 0.3);
    box-shadow:
      0 0 0 1px rgba(251, 191, 36, 0.1),
      0 0 8px rgba(251, 191, 36, 0.1),
      inset 0 0 8px rgba(251, 191, 36, 0.05);
  }
  50% {
    border-color: rgba(251, 191, 36, 0.5);
    box-shadow:
      0 0 0 1px rgba(251, 191, 36, 0.2),
      0 0 15px rgba(251, 191, 36, 0.2),
      0 0 25px rgba(251, 191, 36, 0.1),
      inset 0 0 12px rgba(251, 191, 36, 0.08);
  }
}

/* Clases de utilidad para aplicar las animaciones */
.chat-message-pulse {
  animation: chat-border-pulse 3s ease-in-out infinite;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.chat-message-pulse-user {
  animation: chat-border-pulse-user 3.5s ease-in-out infinite;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.chat-message-pulse-system {
  animation: chat-border-pulse-system 4s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.chat-message-pulse-code {
  animation: chat-border-pulse-code 2.5s ease-in-out infinite;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

/* Variante más sutil para no distraer */
.chat-message-pulse-subtle {
  animation: chat-border-pulse 4s ease-in-out infinite;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Efecto hover para intensificar el brillo */
.chat-message-pulse:hover,
.chat-message-pulse-user:hover,
.chat-message-pulse-system:hover,
.chat-message-pulse-code:hover {
  animation-duration: 2s;
}

/* Animación de entrada para nuevos mensajes con pulso */
@keyframes chat-message-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    border-color: transparent;
    box-shadow: none;
  }
  50% {
    opacity: 0.7;
    transform: translateY(5px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 0 1px rgba(59, 130, 246, 0.1),
      0 0 10px rgba(59, 130, 246, 0.1);
  }
}

.chat-message-appear {
  animation: chat-message-appear 0.6s ease-out;
}

/* Animación especial para mensajes de error */
@keyframes chat-border-pulse-error {
  0%, 100% {
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow:
      0 0 0 1px rgba(239, 68, 68, 0.2),
      0 0 10px rgba(239, 68, 68, 0.2),
      inset 0 0 10px rgba(239, 68, 68, 0.1);
  }
  50% {
    border-color: rgba(239, 68, 68, 0.7);
    box-shadow:
      0 0 0 1px rgba(239, 68, 68, 0.4),
      0 0 20px rgba(239, 68, 68, 0.4),
      0 0 30px rgba(239, 68, 68, 0.2),
      inset 0 0 15px rgba(239, 68, 68, 0.15);
  }
}

.chat-message-pulse-error {
  animation: chat-border-pulse-error 2s ease-in-out infinite;
  border: 1px solid rgba(239, 68, 68, 0.4);
}

/* Animación especial para mensajes de éxito */
@keyframes chat-border-pulse-success {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.2),
      0 0 10px rgba(34, 197, 94, 0.2),
      inset 0 0 10px rgba(34, 197, 94, 0.1);
  }
  50% {
    border-color: rgba(34, 197, 94, 0.7);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.4),
      0 0 20px rgba(34, 197, 94, 0.4),
      0 0 30px rgba(34, 197, 94, 0.2),
      inset 0 0 15px rgba(34, 197, 94, 0.15);
  }
}

.chat-message-pulse-success {
  animation: chat-border-pulse-success 3s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.4);
}

/* Animación especial para mensajes de advertencia */
@keyframes chat-border-pulse-warning {
  0%, 100% {
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow:
      0 0 0 1px rgba(245, 158, 11, 0.2),
      0 0 10px rgba(245, 158, 11, 0.2),
      inset 0 0 10px rgba(245, 158, 11, 0.1);
  }
  50% {
    border-color: rgba(245, 158, 11, 0.7);
    box-shadow:
      0 0 0 1px rgba(245, 158, 11, 0.4),
      0 0 20px rgba(245, 158, 11, 0.4),
      0 0 30px rgba(245, 158, 11, 0.2),
      inset 0 0 15px rgba(245, 158, 11, 0.15);
  }
}

.chat-message-pulse-warning {
  animation: chat-border-pulse-warning 2.8s ease-in-out infinite;
  border: 1px solid rgba(245, 158, 11, 0.4);
}

/* Efecto de intensificación al hacer hover en mensajes especiales */
.chat-message-pulse-error:hover,
.chat-message-pulse-success:hover,
.chat-message-pulse-warning:hover {
  animation-duration: 1.5s;
}

/* Animación de pulso más intensa para mensajes importantes */
.chat-message-pulse-important {
  animation: chat-border-pulse 2s ease-in-out infinite;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

/* ===== EFECTO LÁSER VERDE PARA NOTIFICACIONES ===== */

/* Animación de borde láser verde sutil para notificaciones */
@keyframes notification-laser-pulse {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.3);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.2),
      0 0 8px rgba(34, 197, 94, 0.15),
      0 0 16px rgba(34, 197, 94, 0.1),
      inset 0 0 8px rgba(34, 197, 94, 0.05);
  }
  25% {
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.3),
      0 0 12px rgba(34, 197, 94, 0.2),
      0 0 20px rgba(34, 197, 94, 0.15),
      inset 0 0 10px rgba(34, 197, 94, 0.08);
  }
  50% {
    border-color: rgba(34, 197, 94, 0.5);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.4),
      0 0 16px rgba(34, 197, 94, 0.25),
      0 0 24px rgba(34, 197, 94, 0.2),
      0 0 32px rgba(34, 197, 94, 0.1),
      inset 0 0 12px rgba(34, 197, 94, 0.1);
  }
  75% {
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.3),
      0 0 12px rgba(34, 197, 94, 0.2),
      0 0 20px rgba(34, 197, 94, 0.15),
      inset 0 0 10px rgba(34, 197, 94, 0.08);
  }
}

/* Clase para aplicar el efecto láser verde a notificaciones */
.notification-laser-border {
  animation: notification-laser-pulse 3.5s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.3);
  position: relative;
}

/* Efecto de intensificación sutil al hacer hover */
.notification-laser-border:hover {
  animation-duration: 2.5s;
}

/* Variante más sutil para dispositivos móviles */
@media (max-width: 768px) {
  @keyframes notification-laser-pulse-mobile {
    0%, 100% {
      border-color: rgba(34, 197, 94, 0.25);
      box-shadow:
        0 0 0 1px rgba(34, 197, 94, 0.15),
        0 0 6px rgba(34, 197, 94, 0.1),
        inset 0 0 6px rgba(34, 197, 94, 0.03);
    }
    50% {
      border-color: rgba(34, 197, 94, 0.4);
      box-shadow:
        0 0 0 1px rgba(34, 197, 94, 0.3),
        0 0 12px rgba(34, 197, 94, 0.15),
        0 0 18px rgba(34, 197, 94, 0.1),
        inset 0 0 8px rgba(34, 197, 94, 0.05);
    }
  }

  .notification-laser-border {
    animation: notification-laser-pulse-mobile 4s ease-in-out infinite;
  }
}

/* Variante dorada para notificaciones de etapas importantes */
@keyframes stage-notification-laser-pulse {
  0%, 100% {
    border-color: rgba(251, 191, 36, 0.3);
    box-shadow:
      0 0 0 1px rgba(251, 191, 36, 0.2),
      0 0 8px rgba(251, 191, 36, 0.15),
      0 0 16px rgba(251, 191, 36, 0.1),
      inset 0 0 8px rgba(251, 191, 36, 0.05);
  }
  50% {
    border-color: rgba(251, 191, 36, 0.5);
    box-shadow:
      0 0 0 1px rgba(251, 191, 36, 0.4),
      0 0 16px rgba(251, 191, 36, 0.25),
      0 0 24px rgba(251, 191, 36, 0.2),
      0 0 32px rgba(251, 191, 36, 0.1),
      inset 0 0 12px rgba(251, 191, 36, 0.1);
  }
}

.stage-notification-laser-border {
  animation: stage-notification-laser-pulse 3s ease-in-out infinite;
  border: 1px solid rgba(251, 191, 36, 0.3);
  position: relative;
}

.stage-notification-laser-border:hover {
  animation-duration: 2s;
}

/* ===== ANIMACIONES PARA INTRO ANIMATION ===== */

/* Animaciones para partículas de la intro */
@keyframes particle-fade-in {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(-20px) translateX(-5px);
  }
  75% {
    transform: translateY(-10px) translateX(3px);
  }
}

@keyframes code-particle-fade-in {
  from {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  to {
    opacity: 0.6;
    transform: scale(1) rotate(360deg);
  }
}

@keyframes code-particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(8px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) translateX(-8px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) translateX(5px) rotate(270deg);
  }
}

/* Animaciones para rayos eléctricos */
@keyframes lightning-flash {
  0%, 100% {
    opacity: 0;
    transform: scaleX(0);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

.lightning-horizontal {
  animation: lightning-flash 0.5s ease-in-out;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

.lightning-vertical {
  animation: lightning-flash 0.7s ease-in-out 0.2s;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

.lightning-diagonal-1 {
  animation: lightning-flash 0.6s ease-in-out 0.4s;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

.lightning-diagonal-2 {
  animation: lightning-flash 0.8s ease-in-out 0.6s;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

/* Animación para el título futurista */
@keyframes electric-pulse {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(59, 130, 246, 0.8),
      0 0 10px rgba(59, 130, 246, 0.6),
      0 0 15px rgba(59, 130, 246, 0.4);
  }
  50% {
    text-shadow:
      0 0 10px rgba(59, 130, 246, 1),
      0 0 20px rgba(59, 130, 246, 0.8),
      0 0 30px rgba(59, 130, 246, 0.6),
      0 0 40px rgba(59, 130, 246, 0.4);
  }
}

.electric-pulse {
  animation: electric-pulse 2s ease-in-out infinite;
}

/* Efecto de lluvia de código */
@keyframes code-rain {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

.code-rain {
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 10%,
    rgba(59, 130, 246, 0.3) 50%,
    rgba(59, 130, 246, 0.1) 90%,
    transparent 100%
  );
  animation: code-rain 3s linear infinite;
}

/* Título futurista con efecto de glitch */
.futuristic-title {
  position: relative;
  color: #3b82f6;
  text-shadow:
    0 0 5px rgba(59, 130, 246, 0.8),
    0 0 10px rgba(59, 130, 246, 0.6),
    0 0 15px rgba(59, 130, 246, 0.4);
}

.futuristic-title::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: #60a5fa;
  z-index: -1;
  animation: glitch 2s infinite;
}

@keyframes glitch {
  0%, 90%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  95% {
    transform: translate(2px, -2px);
    opacity: 0.3;
  }
}

.chat-message-pulse-important:hover {
  animation-duration: 1s;
}

/* Animación de entrada con rebote para mensajes especiales */
@keyframes chat-message-bounce-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.8);
    border-color: transparent;
    box-shadow: none;
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.05);
  }
  80% {
    transform: translateY(2px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 0 1px rgba(59, 130, 246, 0.1),
      0 0 10px rgba(59, 130, 246, 0.1);
  }
}

.chat-message-bounce-in {
  animation: chat-message-bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== ANIMACIONES SINCRONIZADAS CON AUDIO ===== */

/* Animación de onda de sonido */
@keyframes sound-wave {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1.5);
    opacity: 1;
  }
}

.sound-wave {
  animation: sound-wave 0.6s ease-in-out infinite;
}

.sound-wave:nth-child(2) {
  animation-delay: 0.1s;
}

.sound-wave:nth-child(3) {
  animation-delay: 0.2s;
}

.sound-wave:nth-child(4) {
  animation-delay: 0.3s;
}

.sound-wave:nth-child(5) {
  animation-delay: 0.4s;
}

/* Animación de partículas flotantes para STORM */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Animación de pulso de audio */
@keyframes audio-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.audio-pulse {
  animation: audio-pulse 2s infinite;
}

/* Animación de espectro de audio */
@keyframes audio-spectrum {
  0%, 100% {
    height: 4px;
  }
  25% {
    height: 12px;
  }
  50% {
    height: 8px;
  }
  75% {
    height: 16px;
  }
}

.audio-spectrum-bar {
  animation: audio-spectrum 0.8s ease-in-out infinite;
  background: linear-gradient(to top, #3b82f6, #60a5fa);
}

.audio-spectrum-bar:nth-child(1) { animation-delay: 0s; }
.audio-spectrum-bar:nth-child(2) { animation-delay: 0.1s; }
.audio-spectrum-bar:nth-child(3) { animation-delay: 0.2s; }
.audio-spectrum-bar:nth-child(4) { animation-delay: 0.3s; }
.audio-spectrum-bar:nth-child(5) { animation-delay: 0.4s; }

/* Animación de brillo para activación de voz */
@keyframes voice-glow {
  0%, 100% {
    box-shadow:
      0 0 5px rgba(59, 130, 246, 0.5),
      0 0 10px rgba(59, 130, 246, 0.3),
      0 0 15px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow:
      0 0 10px rgba(59, 130, 246, 0.8),
      0 0 20px rgba(59, 130, 246, 0.6),
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(59, 130, 246, 0.2);
  }
}

.voice-glow {
  animation: voice-glow 1.5s ease-in-out infinite;
}

/* Animación de ondas concéntricas para STORM */
@keyframes storm-ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

.storm-ripple {
  animation: storm-ripple 1.5s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.storm-ripple:nth-child(2) {
  animation-delay: 0.5s;
}

.storm-ripple:nth-child(3) {
  animation-delay: 1s;
}

/* Animación de typing indicator con audio */
@keyframes typing-audio {
  0%, 60%, 100% {
    transform: translateY(0);
    background-color: rgba(59, 130, 246, 0.4);
  }
  30% {
    transform: translateY(-10px);
    background-color: rgba(59, 130, 246, 1);
  }
}

.typing-audio {
  animation: typing-audio 1.4s ease-in-out infinite;
}

.typing-audio:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-audio:nth-child(3) {
  animation-delay: 0.4s;
}

/* Animación de feedback visual para efectos de sonido */
@keyframes sound-feedback {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.3);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
}

.sound-feedback {
  animation: sound-feedback 0.3s ease-out;
}

/* Animación de visualizador de volumen */
@keyframes volume-bar {
  0%, 100% {
    height: 20%;
    background-color: rgba(59, 130, 246, 0.3);
  }
  50% {
    height: 100%;
    background-color: rgba(59, 130, 246, 1);
  }
}

.volume-bar {
  animation: volume-bar 0.5s ease-in-out infinite;
}

.volume-bar:nth-child(1) { animation-delay: 0s; }
.volume-bar:nth-child(2) { animation-delay: 0.1s; }
.volume-bar:nth-child(3) { animation-delay: 0.2s; }
.volume-bar:nth-child(4) { animation-delay: 0.3s; }
.volume-bar:nth-child(5) { animation-delay: 0.4s; }

/* Animación de sincronización de labios para TTS */
@keyframes lip-sync {
  0%, 100% {
    transform: scaleY(1);
  }
  25% {
    transform: scaleY(0.8);
  }
  50% {
    transform: scaleY(1.2);
  }
  75% {
    transform: scaleY(0.9);
  }
}

.lip-sync {
  animation: lip-sync 0.4s ease-in-out infinite;
}
