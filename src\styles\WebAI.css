/* Estilos específicos para la página WebAI */
.bg-codestorm-darker {
  background-color: #050a14;
  color: white;
}

.bg-codestorm-dark {
  background-color: #0a1120;
  color: white;
}

/* Asegurarse de que los componentes sean visibles */
.min-h-screen {
  min-height: 100vh;
}

/* Estilos para el contenedor principal */
.container {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}

/* Estilos para la cuadrícula */
.grid {
  display: grid;
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

/* Estilos para los paneles colapsables */
.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Estilos para el editor de código */
.h-full {
  height: 100%;
}

.flex-1 {
  flex: 1 1 0%;
}

.overflow-hidden {
  overflow: hidden;
}

/* Estilos para los botones */
.rounded-md {
  border-radius: 0.375rem;
}

.p-4 {
  padding: 1rem;
}

/* Estilos para el texto */
.text-center {
  text-align: center;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Estilos para los elementos flexibles */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

/* Estilos para los márgenes */
.mt-2 {
  margin-top: 0.5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Estilos para la opacidad */
.opacity-20 {
  opacity: 0.2;
}

/* Estilos para el terminal */
.h-1\/3 {
  height: 33.333333%;
}

.mt-4 {
  margin-top: 1rem;
}

/* Estilos para los botones flotantes */
.fixed {
  position: fixed;
}

.bottom-4 {
  bottom: 1rem;
}

.right-4 {
  right: 1rem;
}

.z-50 {
  z-index: 50;
}

/* Estilos para el logo */
.bottom-8 {
  bottom: 2rem;
}

.right-8 {
  right: 2rem;
}

/* Estilos para el pie de página */
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Estilos para asegurar que los componentes sean visibles */
.webai-component {
  visibility: visible !important;
  display: block !important;
  opacity: 1 !important;
}
