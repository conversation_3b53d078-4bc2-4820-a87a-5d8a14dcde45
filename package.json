{"name": "autonomous-python-dev-agent", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --config vite.config.local.ts --port 5173", "dev:public": "vite --config vite.config.public.ts --host 0.0.0.0 --port 5173", "build": "vite build", "build:public": "vite build --config vite.config.public.ts", "lint": "eslint .", "preview": "vite preview", "proxy": "node --no-warnings server.js", "start": "concurrently \"npm run proxy\" \"npm run dev\"", "start:public": "concurrently \"npm run proxy\" \"npm run dev:public\"", "setup:local": "node setup-public.js local", "setup:public": "node setup-public.js public"}, "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@google/generative-ai": "^0.2.1", "@monaco-editor/react": "^4.7.0", "@types/diff": "^8.0.0", "@types/jszip": "^3.4.1", "cors": "^2.8.5", "diff": "^8.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "file-saver": "^2.0.5", "http-proxy-middleware": "^2.0.6", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "openai": "^4.28.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.0", "react-syntax-highlighter": "^15.5.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/file-saver": "^2.0.7", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}