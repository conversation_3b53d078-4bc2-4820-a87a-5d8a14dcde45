/* Importar animaciones personalizadas */
@import './styles/animations.css';

/* Importar optimizaciones móviles */
@import './styles/mobile.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Animación de pulso sutil */
  .animate-pulse-subtle {
    animation: pulse-subtle 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-subtle {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.85;
    }
  }

  /* Sombra con resplandor azul */
  .shadow-glow-blue {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  }

  /* Animación de resplandor */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    to {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
  }

  /* Animación de aparición con escala */
  .animate-scale-in {
    animation: scale-in 0.3s ease-out forwards;
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Animación de desaparición con escala */
  .animate-scale-out {
    animation: scale-out 0.3s ease-in forwards;
  }

  @keyframes scale-out {
    from {
      opacity: 1;
      transform: scale(1);
    }
    to {
      opacity: 0;
      transform: scale(0.95);
    }
  }

  /* Animación de entrada desde abajo */
  .animate-slide-up {
    animation: slide-up 0.4s ease-out forwards;
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
