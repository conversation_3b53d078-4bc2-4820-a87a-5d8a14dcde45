# CODESTORM

<div align="center">
  <img src="public/botidinamix-logo.svg" alt="BOTIDINAMIX Logo" width="150" height="150"/>
  <h3>Plataforma de Desarrollo Autónomo Impulsada por IA</h3>
  <p><i>Desarrollado por BOTIDINAMIX AI</i></p>

  <!-- Video de demostración -->
  <video width="80%" controls>
    <source src="public/codestorm.mp4" type="video/mp4">
    Tu navegador no soporta el elemento de video.
  </video>
  <p><i>Video de demostración de CODESTORM en acción</i></p>
</div>

## 🌩️ ¿Qué es CODESTORM?

CODESTORM es una plataforma avanzada de desarrollo autónomo que revoluciona la forma en que se crean aplicaciones y soluciones de software. Utilizando un sistema de agentes especializados impulsados por IA, CODESTORM automatiza y optimiza todo el proceso de desarrollo, desde la planificación inicial hasta la generación y organización del código.

La plataforma está diseñada para:
- **Acelerar el desarrollo** de proyectos complejos mediante la automatización inteligente
- **Mejorar la calidad del código** a través de análisis y optimizaciones continuas
- **Facilitar la colaboración** entre humanos e IA en el proceso de desarrollo
- **Proporcionar una experiencia interactiva** con retroalimentación en tiempo real

## ✨ Características Principales

### 🤖 Sistema de Agentes Especializados

CODESTORM implementa una arquitectura modular con agentes especializados que trabajan en conjunto:

| Agente | Función |
|--------|---------|
| **Agente de Planificación** | Analiza los requisitos y crea planes detallados de desarrollo |
| **Agente de Generación de Código** | Produce código de alta calidad basado en los planes establecidos |
| **Agente de Sincronización de Archivos** | Mantiene sincronizados los archivos entre el terminal y el explorador |
| **Agente de Modificación de Código** | Realiza cambios inteligentes en el código existente con actualización automática del preview |
| **Agente de Observación de Archivos** | Monitorea en tiempo real los archivos creados y su estructura |
| **Agente de Separación de Código** | Segmenta automáticamente código combinado en archivos individuales |
| **Agente de Corrección de Código** | Detecta y corrige errores de sintaxis, lógica, seguridad y rendimiento |
| **Agente de Seguimiento** | Documenta el proceso de desarrollo y mantiene un registro del progreso |
| **Agente Lector** | Analiza archivos existentes y proporciona contexto para modificaciones |
| **Agente de Diseño Arquitectónico** | Genera automáticamente archivos HTML/CSS para interfaces visuales |

### 🔍 Función "Enhance Prompt"

Una característica innovadora que mejora automáticamente las instrucciones del usuario antes de enviarlas al sistema:
- Clarifica requisitos ambiguos
- Añade detalles técnicos relevantes
- Optimiza la estructura de la solicitud
- Mejora la precisión de los resultados generados
- Proporciona retroalimentación visual durante el proceso de mejora

### 🏗️ Constructor con Sistema de Aprobación por Etapas

Un flujo de trabajo conversacional que otorga al usuario control total sobre el proceso de creación:
- Sistema de aprobación por etapas para validar cada fase del desarrollo
- Chat interactivo avanzado para comunicación fluida con los agentes
- Corrector de código inteligente que sugiere mejoras y soluciones
- Visualización en tiempo real del progreso del proyecto
- Métricas detalladas del avance por etapas y tipos de archivos

### 🔧 Corrector de Código Avanzado

Un potente sistema de análisis y corrección de código con características avanzadas:
- Detección inteligente de errores de sintaxis, lógica, seguridad y rendimiento
- Panel visual de resultados con diferenciación clara de cambios (adiciones, eliminaciones, modificaciones)
- Resaltado de sintaxis adaptado al lenguaje de programación detectado
- Opciones para copiar, descargar o aplicar los cambios directamente
- Resumen estadístico de correcciones realizadas por tipo y severidad
- Interfaz completamente responsive con animaciones sutiles

### ✂️ Separador de Código

Herramienta especializada que:
- Analiza bloques de código extensos
- Identifica componentes lógicos y funcionales
- Separa el código en archivos independientes con la estructura adecuada
- Mantiene la coherencia entre los archivos generados

### 👁️ Observador de Archivos

Sistema de monitoreo que:
- Analiza en tiempo real la estructura y contenido de los archivos
- Proporciona visualizaciones del árbol de archivos
- Mejora las sugerencias durante el desarrollo
- Comunica información relevante a otros agentes del sistema

### 🎨 Interfaz Moderna y Responsiva

- Diseño adaptable para dispositivos móviles y de escritorio
- Animaciones sutiles inspiradas en el concepto de "tormenta de código"
- Paneles colapsables para optimizar el espacio de trabajo
- Botones flotantes mejorados con navegación al menú principal
- Logo de BOTIDINAMIX AI posicionado en la esquina superior izquierda
- Temas visuales personalizables con efectos futuristas

### 🔄 Actualización Automática del Preview

Una característica revolucionaria que mejora la experiencia de desarrollo:

- **Detección inteligente** de archivos estáticos (HTML, CSS, JavaScript)
- **Actualización en tiempo real** del CodePreview/WebView cuando se modifican archivos
- **Sincronización automática** entre modificaciones y vista previa
- **Feedback visual inmediato** sin intervención manual del usuario
- **Comunicación eficiente** mediante eventos personalizados entre componentes

### 🧩 Segmentación Automática de Código

Sistema avanzado de separación de archivos integrado en toda la plataforma:

- **Detección automática** de código combinado que necesita segmentación
- **Análisis inteligente** por patrones de comentarios, bloques de código y declaraciones
- **Generación de archivos individuales** perfectamente organizados
- **Consistencia total** entre página principal y Constructor
- **Manejo robusto** de errores con fallbacks seguros

### 🏗️ Generación Automática de Interfaces Visuales

El DesignArchitectAgent ahora está integrado en toda la plataforma:

- **Generación automática** de archivos HTML/CSS para TODAS las aplicaciones
- **Detección inteligente** de colores y paletas profesionales por industria
- **Diseño responsivo** mobile-first con estructura HTML5 semántica
- **Variables CSS** organizadas y cumplimiento de accesibilidad WCAG 2.1 AA
- **Integración transparente** sin afectar la funcionalidad existente

### 🎤 Sistema de Reconocimiento de Voz Avanzado

CODESTORM incluye un sistema completo de reconocimiento de voz optimizado para español:

- **Reconocimiento nativo** usando la API Speech Recognition del navegador
- **Configuración optimizada** para español (es-ES) con alta precisión
- **Sistema de coordinación** que evita conflictos entre múltiples instancias
- **Auto-reparación inteligente** que diagnostica y corrige problemas automáticamente
- **Diagnóstico completo** con herramientas de depuración integradas
- **Compatibilidad universal** con Chrome, Edge, Safari y dispositivos móviles
- **Manejo robusto de errores** con recuperación automática

#### Características del Sistema de Voz:

- **VoiceCoordinator**: Gestiona acceso exclusivo y previene conflictos
- **UnifiedVoiceService**: Servicio centralizado con configuración optimizada
- **Auto-reparación**: Sistema que detecta y corrige problemas automáticamente
- **Diagnóstico visual**: Componente para identificar y resolver problemas
- **Filtros de post-procesamiento**: Mejoran la precisión de transcripción

### 🌐 Acceso Público y Configuración de Red

CODESTORM puede ser configurado para acceso público desde cualquier dispositivo:

- **Configuración automática** para exposición pública con IP específica
- **Scripts dedicados** para modo local y público
- **Configuración CORS** optimizada para acceso externo
- **Documentación completa** para configuración de firewall y router
- **Soporte para HTTPS** y conexiones seguras
- **Compatibilidad móvil** completa para acceso remoto

## 🛠️ Tecnologías Utilizadas

- **Frontend**: React, TypeScript, Tailwind CSS
- **Modelos de IA**: Claude (Anthropic), GPT (OpenAI), Gemini (Google)
- **Herramientas de Desarrollo**: Vite, ESLint, TypeScript
- **Backend**: Express (servidor proxy para APIs)
- **Utilidades**: JSZip, File-Saver, React Router, XTerm
- **Análisis de Código**: Diff, SyntaxHighlighter, Prism

## 🚀 Instalación y Configuración

### Requisitos Previos

- Node.js (v16 o superior)
- npm (v8 o superior) o yarn (v1.22 o superior)
- Acceso a las APIs de los modelos de IA (OpenAI, Anthropic, Google)

### Paso a Paso

1. **Clonar el Repositorio**

   ```bash
   git clone https://github.com/tu-usuario/codestorm.git
   cd codestorm
   ```

2. **Instalar Dependencias**

   ```bash
   npm install
   # o
   yarn install
   ```

3. **Configurar Variables de Entorno**

   Crea un archivo `.env` en la raíz del proyecto:

   ```env
   VITE_OPENAI_API_KEY=tu_clave_de_openai
   VITE_ANTHROPIC_API_KEY=tu_clave_de_anthropic
   VITE_GEMINI_API_KEY=tu_clave_de_gemini
   PORT=3001
   ```

4. **Iniciar la Aplicación**

   #### Modo Local (Desarrollo):
   ```bash
   npm run start
   # o
   yarn start
   ```

   #### Modo Público (Acceso Externo):
   ```bash
   # Configurar automáticamente para acceso público
   npm run setup:public

   # Iniciar en modo público
   npm run start:public
   ```

   Esto iniciará tanto el servidor proxy como la aplicación frontend.

5. **Acceder a la Aplicación**

   #### Acceso Local:
   ```url
   http://localhost:5173
   ```

   #### Acceso Público (cuando esté configurado):
   ```url
   http://************:5173
   ```

   **Nota**: Para acceso público, asegúrate de que los puertos 3001 y 5173 estén abiertos en tu firewall y router.

## 🌐 Configuración de Acceso Público

CODESTORM puede ser configurado para acceso público desde cualquier dispositivo en la red. Consulta el archivo `PUBLIC_DEPLOYMENT.md` para instrucciones detalladas.

### Scripts Disponibles:

- `npm run setup:public` - Configura automáticamente para acceso público
- `npm run setup:local` - Vuelve a configuración local
- `npm run start:public` - Inicia en modo público
- `npm run start` - Inicia en modo local

### Requisitos para Acceso Público:

1. **Firewall**: Abrir puertos 3001 y 5173
2. **Router**: Configurar port forwarding si es necesario
3. **Variables de entorno**: Mantener seguras las API keys
4. **Conexión**: Preferiblemente HTTPS para producción

## 🎤 Sistema de Reconocimiento de Voz

CODESTORM incluye un sistema avanzado de reconocimiento de voz con las siguientes características:

### Funcionalidades:

- **Reconocimiento optimizado** para español (es-ES)
- **Auto-reparación** cuando ocurren errores
- **Diagnóstico visual** para identificar problemas
- **Coordinación inteligente** entre múltiples instancias
- **Compatibilidad universal** con navegadores modernos

### Solución de Problemas:

Si experimentas problemas con el reconocimiento de voz:

1. **Verifica permisos** de micrófono en tu navegador
2. **Usa el diagnóstico** integrado para identificar problemas
3. **Ejecuta auto-reparación** desde cualquier componente de chat
4. **Consulta la consola** para mensajes de debug detallados

### Navegadores Compatibles:

- ✅ **Chrome** (Recomendado)
- ✅ **Edge** (Excelente soporte)
- ✅ **Safari** (iOS 14.5+ / macOS Big Sur+)
- ⚠️ **Firefox** (Soporte limitado)

## 📂 Estructura del Proyecto

```plaintext
codestorm/
├── public/                  # Archivos estáticos
│   ├── botidinamix-logo.svg # Logo de BOTIDINAMIX
│   └── ...                  # Otros recursos estáticos
├── src/                     # Código fuente
│   ├── agents/              # Implementación de agentes IA
│   ├── components/          # Componentes React
│   │   ├── constructor/     # Componentes del Constructor
│   │   └── ...              # Otros componentes
│   ├── contexts/            # Contextos de React
│   ├── data/                # Datos estáticos
│   ├── pages/               # Páginas principales
│   ├── services/            # Servicios (AI, terminal)
│   ├── App.tsx              # Componente principal
│   └── main.tsx             # Punto de entrada
├── server.js                # Servidor proxy para APIs
├── package.json             # Dependencias y scripts
├── tailwind.config.js       # Configuración de Tailwind
└── vite.config.js           # Configuración de Vite
```

## 📈 Changelog - Últimas Mejoras

### 🆕 Versión 3.0.0 - Enero 2025

#### ✨ **Nuevas Características Principales:**

- **🎤 Sistema de Reconocimiento de Voz Avanzado**: Implementación completa con auto-reparación, diagnóstico y coordinación inteligente
- **🌐 Configuración de Acceso Público**: Scripts automáticos para exposición pública con IP específica (************)
- **🔧 Auto-Reparación de Voz**: Sistema que detecta y corrige automáticamente problemas de reconocimiento de voz
- **📊 Diagnóstico Visual**: Componente completo para identificar y resolver problemas del sistema de voz
- **🔄 Actualización Automática del Preview**: Los cambios en archivos estáticos se reflejan automáticamente en tiempo real
- **🧩 Segmentación Automática de Código**: Separación inteligente de código combinado en archivos individuales

#### 🎤 **Sistema de Voz Completo:**

- **VoiceCoordinator**: Gestión de acceso exclusivo y prevención de conflictos
- **UnifiedVoiceService**: Servicio centralizado optimizado para español (es-ES)
- **Verificación de permisos permisiva**: No bloquea la inicialización por problemas menores
- **Manejo inteligente de errores**: Diferenciación entre errores críticos y recuperables
- **Auto-reparación automática**: Recuperación tras fallos de inicialización
- **Diagnóstico completo**: Herramientas visuales para identificar problemas

#### 🌐 **Configuración de Red Pública:**

- **Scripts dedicados**: `npm run setup:public` y `npm run start:public`
- **Configuración automática**: Cambio entre modo local y público
- **CORS optimizado**: Configuración específica para IP pública
- **Documentación completa**: Guías para firewall y router
- **Soporte móvil**: Acceso completo desde dispositivos móviles

#### 🔧 **Mejoras Técnicas:**

- **Inicialización con delay**: Evita problemas de concurrencia en el arranque
- **Cleanup robusto**: Manejo seguro de recursos con control de excepciones
- **Sincronización de estado**: Coordinación eficiente entre componentes
- **Filtros de post-procesamiento**: Mejora la precisión de transcripción
- **Compatibilidad universal**: Chrome, Edge, Safari y dispositivos móviles

#### 🐛 **Correcciones Críticas:**

- **Solucionados errores de inicialización de voz**: Sistema más robusto y tolerante a fallos
- **Corregidos problemas de permisos**: Verificación no bloqueante de micrófono
- **Mejorado manejo de errores**: Recuperación automática de estados de error
- **Optimizada coordinación**: Prevención de conflictos entre servicios de voz
- **Estabilizada sincronización**: Mejor comunicación entre componentes

### 🆕 Versión 2.5.0 - Enero 2025

#### ✨ **Características Anteriores:**

- **🏗️ Generación Automática de Interfaces**: DesignArchitectAgent integrado
- **🏠 Botón de Menú Principal**: Navegación rápida mejorada
- **📍 Logo Reposicionado**: Mejor accesibilidad visual
- **Optimización móvil**: Mejoras en responsividad y navegación

## 📸 Capturas de Pantalla

<div align="center">
  <img src="https://i.imgur.com/example1.png" alt="Interfaz Principal" width="80%"/>
  <p><i>Interfaz Principal de CODESTORM con nuevas características</i></p>

  <img src="https://i.imgur.com/example2.png" alt="Constructor" width="80%"/>
  <p><i>Constructor con Segmentación Automática y Generación de Interfaces</i></p>

  <img src="https://i.imgur.com/example3.png" alt="Preview Automático" width="80%"/>
  <p><i>Actualización Automática del Preview en Tiempo Real</i></p>

  <img src="https://i.imgur.com/example4.png" alt="Botones Flotantes" width="80%"/>
  <p><i>Panel de Botones Flotantes Mejorado con Navegación</i></p>
</div>

## 🤝 Contribuir

Las contribuciones son bienvenidas y apreciadas. Para contribuir:

1. Haz un fork del repositorio
2. Crea una rama para tu característica (`git checkout -b feature/amazing-feature`)
3. Realiza tus cambios y haz commit (`git commit -m 'Add some amazing feature'`)
4. Sube tus cambios (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT - consulta el archivo [LICENSE](LICENSE) para más detalles.

## 🙏 Agradecimientos

- Desarrollado por el equipo de BOTIDINAMIX AI
- Inspirado en las necesidades reales de desarrolladores
- Impulsado por modelos de IA de vanguardia

---

<div align="center">
  <p>BOTIDINAMIX AI - Todos los derechos reservados © 2025</p>
</div>
